<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 dark:text-gray-200 leading-tight">
                    @if(auth()->user()->hasRole('admin'))
                        Admin Dashboard
                    @elseif(auth()->user()->hasRole('editor'))
                        Editor Dashboard
                    @else
                        Dashboard
                    @endif
                </h2>
                <p class="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    Welcome back, {{ auth()->user()->name }}! Here's what's happening today.
                </p>
            </div>
            <div class="flex items-center space-x-3">
                <div class="text-right">
                    <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ now()->format('l, F j, Y') }}</div>
                    <div class="text-xs text-gray-500 dark:text-gray-400">{{ now()->format('g:i A') }}</div>
                </div>
                <div class="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                    <span class="text-white font-medium text-sm">{{ substr(auth()->user()->name, 0, 1) }}</span>
                </div>
            </div>
        </div>
    </x-slot>

    <div class="py-8">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            @if(auth()->user()->hasRole('admin'))
                @include('partials.admin-dashboard')
            @elseif(auth()->user()->hasRole('editor'))
                @include('partials.editor-dashboard')
            @else
                @include('partials.user-dashboard')
            @endif
        </div>
    </div>
</x-app-layout>
