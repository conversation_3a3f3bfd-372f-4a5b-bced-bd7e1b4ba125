<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\AdminController;
use App\Http\Controllers\PageController;
use App\Http\Controllers\CategoryController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\UserManagementController;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome-new');
});

Route::get('/dashboard', function () {
    $user = auth()->user();

    // Admin dashboard with full stats
    if ($user->hasRole('admin')) {
        $stats = [
            'users' => \App\Models\User::count(),
            'pages' => \App\Models\Page::count(),
            'categories' => \App\Models\Category::count(),
            'tags' => \App\Models\Tag::count(),
            'roles' => \Spatie\Permission\Models\Role::count(),
            'permissions' => \Spatie\Permission\Models\Permission::count(),
        ];

        $recentUsers = \App\Models\User::latest()->take(5)->get();
        $recentPages = \App\Models\Page::with(['category', 'author'])->latest()->take(5)->get();
        $recentCategories = \App\Models\Category::latest()->take(5)->get();
        $popularTags = \App\Models\Tag::popular(5)->get();

        return view('dashboard', compact('stats', 'recentUsers', 'recentPages', 'recentCategories', 'popularTags'));
    }

    // Editor dashboard with limited stats
    if ($user->hasRole('editor')) {
        $stats = [
            'my_pages' => \App\Models\Page::where('author_id', $user->id)->count(),
            'published_pages' => \App\Models\Page::where('author_id', $user->id)->where('status', true)->count(),
            'draft_pages' => \App\Models\Page::where('author_id', $user->id)->where('status', false)->count(),
        ];

        $myRecentPages = \App\Models\Page::with(['category'])->where('author_id', $user->id)->latest()->take(5)->get();

        return view('dashboard', compact('stats', 'myRecentPages'));
    }

    // Regular user dashboard
    return view('dashboard');
})->middleware(['auth'])->name('dashboard');

Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
});

// Management routes (role-based access)
Route::middleware(['auth'])->group(function () {
    // User management (admin only)
    Route::middleware('role:admin')->group(function () {
        Route::resource('users', UserManagementController::class);
        Route::resource('roles', RoleController::class);
    });

    // Category management (admin only)
    Route::middleware('role:admin')->group(function () {
        Route::resource('categories', CategoryController::class);
        Route::get('categories/{category}/subcategories', [CategoryController::class, 'getSubcategories'])->name('categories.subcategories');
    });

    // Tag management (admin only)
    Route::middleware('role:admin')->group(function () {
        Route::resource('tags', TagController::class);
        Route::get('tags/search', [TagController::class, 'search'])->name('tags.search');
    });

    // Page management (admin and editor)
    Route::middleware('role:admin|editor')->group(function () {
        Route::get('manage/pages', [PageController::class, 'adminIndex'])->name('pages.manage');
        Route::get('manage/pages/create', [PageController::class, 'create'])->name('pages.create');
        Route::post('manage/pages', [PageController::class, 'store'])->name('pages.store');
        Route::get('manage/pages/{page}/edit', [PageController::class, 'edit'])->name('pages.edit');
        Route::put('manage/pages/{page}', [PageController::class, 'update'])->name('pages.update');
        Route::delete('manage/pages/{page}', [PageController::class, 'destroy'])->name('pages.destroy');
    });
});

require __DIR__.'/auth.php';

// SEO-friendly public routes
Route::get('/pages', [PageController::class, 'index'])->name('pages.index');
Route::get('/category/{category}', [PageController::class, 'byCategory'])->name('pages.category');
Route::get('/tag/{tag}', [PageController::class, 'byTag'])->name('pages.tag');
Route::get('/{category}/{page}', [PageController::class, 'show'])->name('pages.show')->where(['category' => '[a-zA-Z0-9\-]+', 'page' => '[a-zA-Z0-9\-]+']);
Route::get('/{page}', [PageController::class, 'showWithoutCategory'])->name('pages.show.simple')->where('page', '[a-zA-Z0-9\-]+');
