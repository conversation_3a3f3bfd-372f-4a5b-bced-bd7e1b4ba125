<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Category;
use App\Models\Tag;

class CategoryTagSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Categories
        $technology = Category::create([
            'category' => 'Technology',
            'slug' => 'technology',
            'description' => 'Technology related content',
            'status' => true,
            'sort_order' => 1
        ]);

        $webDev = Category::create([
            'category' => 'Web Development',
            'slug' => 'web-development',
            'parent_category' => $technology->id,
            'description' => 'Web development tutorials and guides',
            'status' => true,
            'sort_order' => 1
        ]);

        Category::create([
            'category' => 'Frontend',
            'slug' => 'frontend',
            'parent_category' => $webDev->id,
            'description' => 'Frontend development',
            'status' => true,
            'sort_order' => 1
        ]);

        Category::create([
            'category' => 'Backend',
            'slug' => 'backend',
            'parent_category' => $webDev->id,
            'description' => 'Backend development',
            'status' => true,
            'sort_order' => 2
        ]);

        $mobileDev = Category::create([
            'category' => 'Mobile Development',
            'slug' => 'mobile-development',
            'parent_category' => $technology->id,
            'description' => 'Mobile app development',
            'status' => true,
            'sort_order' => 2
        ]);

        Category::create([
            'category' => 'iOS',
            'slug' => 'ios',
            'parent_category' => $mobileDev->id,
            'description' => 'iOS development',
            'status' => true,
            'sort_order' => 1
        ]);

        Category::create([
            'category' => 'Android',
            'slug' => 'android',
            'parent_category' => $mobileDev->id,
            'description' => 'Android development',
            'status' => true,
            'sort_order' => 2
        ]);

        $business = Category::create([
            'category' => 'Business',
            'slug' => 'business',
            'description' => 'Business related content',
            'status' => true,
            'sort_order' => 2
        ]);

        Category::create([
            'category' => 'Marketing',
            'slug' => 'marketing',
            'parent_category' => $business->id,
            'description' => 'Marketing strategies and tips',
            'status' => true,
            'sort_order' => 1
        ]);

        Category::create([
            'category' => 'Finance',
            'slug' => 'finance',
            'parent_category' => $business->id,
            'description' => 'Financial advice and tips',
            'status' => true,
            'sort_order' => 2
        ]);

        // Create Tags
        $tags = [
            ['name' => 'Laravel', 'color' => '#FF2D20'],
            ['name' => 'PHP', 'color' => '#777BB4'],
            ['name' => 'JavaScript', 'color' => '#F7DF1E'],
            ['name' => 'Vue.js', 'color' => '#4FC08D'],
            ['name' => 'React', 'color' => '#61DAFB'],
            ['name' => 'Alpine.js', 'color' => '#8BC34A'],
            ['name' => 'Tailwind CSS', 'color' => '#06B6D4'],
            ['name' => 'MySQL', 'color' => '#4479A1'],
            ['name' => 'Tutorial', 'color' => '#9C27B0'],
            ['name' => 'Beginner', 'color' => '#4CAF50'],
            ['name' => 'Advanced', 'color' => '#FF5722'],
            ['name' => 'Tips', 'color' => '#FF9800'],
            ['name' => 'Best Practices', 'color' => '#2196F3'],
            ['name' => 'Security', 'color' => '#F44336'],
            ['name' => 'Performance', 'color' => '#795548'],
        ];

        foreach ($tags as $tag) {
            Tag::create([
                'name' => $tag['name'],
                'slug' => \Str::slug($tag['name']),
                'color' => $tag['color'],
                'status' => true,
            ]);
        }
    }
}
