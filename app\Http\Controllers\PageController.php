<?php

namespace App\Http\Controllers;

use App\Models\Page;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class PageController extends Controller
{
    public function __construct()
    {
    }

    public function index()
    {
        $pages = Page::published()->latest()->paginate(10);
        return view('backend.pages.index', compact('pages'));
    }

    public function adminIndex()
    {
        $user = auth()->user();

        if ($user->hasRole('admin')) {
            $pages = Page::with(['author', 'category'])->latest()->paginate(10);
        } else {
            // Editors can only see their own pages
            $pages = Page::with(['author', 'category'])->where('author_id', $user->id)->latest()->paginate(10);
        }

        return view('backend.pages.manage', compact('pages'));
    }

    public function byCategory(Category $category)
    {
        $pages = Page::with(['author', 'tags'])
                    ->where('category_id', $category->id)
                    ->published()
                    ->latest()
                    ->paginate(12);

        return view('backend.pages.category', compact('pages', 'category'));
    }

    public function byTag(Tag $tag)
    {
        $pages = $tag->pages()
                    ->with(['author', 'category'])
                    ->published()
                    ->latest()
                    ->paginate(12);

        return view('backend.pages.tag', compact('pages', 'tag'));
    }

    public function showWithoutCategory($slug)
    {
        $page = Page::with(['author', 'category', 'tags'])
                   ->where('slug', $slug)
                   ->firstOrFail();

        if (!$page->status && !auth()->user()?->hasAnyRole(['admin', 'editor'])) {
            abort(404);
        }

        // Increment views
        $page->increment('views');

        return view('backend.pages.show', compact('page'));
    }

    public function create()
    {
        $categories = \App\Models\Category::active()->orderBy('category')->get();
        $tags = \App\Models\Tag::active()->orderBy('name')->get();

        return view('backend.pages.create', compact('categories', 'tags'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required',
            'category_id' => 'nullable|exists:categories,id',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string',
            'status' => 'boolean',
            'featured_image' => 'nullable|image|max:2048',
            'tags' => 'array',
            'tags.*' => 'exists:tags,id'
        ]);

        $slug = Str::slug($request->title);
        $originalSlug = $slug;
        $counter = 1;

        while (Page::where('slug', $slug)->exists()) {
            $slug = $originalSlug . '-' . $counter;
            $counter++;
        }

        $data = $request->all();
        $data['slug'] = $slug;
        $data['author_id'] = auth()->id();
        $data['status'] = $request->has('status');

        if ($request->hasFile('featured_image')) {
            $data['featured_image'] = $request->file('featured_image')->store('pages', 'public');
        }

        $page = Page::create($data);

        // Sync tags if provided
        if ($request->tags) {
            $page->syncTags($request->tags);
        }

        return redirect()->route('pages.manage')
            ->with('success', 'Page created successfully.');
    }

    public function show($categorySlug, $pageSlug)
    {
        $category = Category::where('slug', $categorySlug)->firstOrFail();
        $page = Page::with(['author', 'category', 'tags'])
                   ->where('slug', $pageSlug)
                   ->where('category_id', $category->id)
                   ->firstOrFail();

        if (!$page->status && !auth()->user()?->hasAnyRole(['admin', 'editor'])) {
            abort(404);
        }

        // Increment views
        $page->increment('views');

        return view('pages.show', compact('page', 'category'));
    }

    public function edit(Page $page)
    {
        $categories = \App\Models\Category::active()->orderBy('category')->get();
        $tags = \App\Models\Tag::active()->orderBy('name')->get();

        return view('backend.pages.edit', compact('page', 'categories', 'tags'));
    }

    public function update(Request $request, Page $page)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required',
            'category_id' => 'nullable|exists:categories,id',
            'meta_description' => 'nullable|string|max:160',
            'meta_keywords' => 'nullable|string',
            'status' => 'boolean',
            'featured_image' => 'nullable|image|max:2048',
            'tags' => 'array',
            'tags.*' => 'exists:tags,id'
        ]);

        $data = $request->all();
        $data['status'] = $request->has('status');

        if ($request->title !== $page->title) {
            $slug = Str::slug($request->title);
            $originalSlug = $slug;
            $counter = 1;

            while (Page::where('slug', $slug)->where('id', '!=', $page->id)->exists()) {
                $slug = $originalSlug . '-' . $counter;
                $counter++;
            }
            $data['slug'] = $slug;
        }

        if ($request->hasFile('featured_image')) {
            if ($page->featured_image) {
                \Storage::disk('public')->delete($page->featured_image);
            }
            $data['featured_image'] = $request->file('featured_image')->store('pages', 'public');
        }

        $page->update($data);

        // Sync tags
        $page->syncTags($request->tags ?? []);

        return redirect()->route('pages.manage')
            ->with('success', 'Page updated successfully.');
    }

    public function destroy(Page $page)
    {
        if ($page->featured_image) {
            \Storage::disk('public')->delete($page->featured_image);
        }

        $page->delete();

        return redirect()->route('pages.manage')
            ->with('success', 'Page deleted successfully.');
    }
}
